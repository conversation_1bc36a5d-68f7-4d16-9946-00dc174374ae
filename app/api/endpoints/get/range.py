from typing import Optional, Dict, <PERSON>, <PERSON><PERSON>, List

import pandas as pd
import pytz
from datetime import datetime
from fastapi import APIRouter

from app.core.refresh_source_db_by_range import refresh_source_db_by_range
from app.core.refresh_aggregated_db_by_range import refresh_aggregated_db_by_range
from app.core.helpers.convert_date_range_to_candles import convert_date_range_to_candles
from app.core.helpers.convert_date_to_utc_timestamp import convert_date_to_utc_timestamp
from app.api.endpoints.get.helpers.get_missing_timestamps import get_missing_timestamps
from app.api.endpoints.get.helpers.is_data_complete import is_data_complete
from app.db.sqlite.get_from_db_by_timestamp import get_from_db_by_timestamp
from app.logger.get_logger import logger, log
from app.config import EXCLUDE_CURRENT_CANDLE_BITSTAMP
from app.core.helpers.validate_currency_pair import validate_currency_pair_or_raise

router = APIRouter()



@router.get("/{currency_pair}/{timeframe}/{from_date}/{to_date}")
@log
def get_range_endpoint(currency_pair: str, timeframe: str, from_date: str, to_date: str) -> Dict[str, Any]:
    """
    Get OHLC candles for a given currency pair, timeframe and date range.

    Flow:
    1. Check aggregated database first (cache layer)
    2. If missing/stale, try to aggregate from source databases
    3. If source databases missing data, refresh from APIs
    4. Re-aggregate and return with source metadata
    """
    api_call_timestamp = int(datetime.now(pytz.utc).timestamp())
    ecc = EXCLUDE_CURRENT_CANDLE_BITSTAMP

    from_timestamp = convert_date_to_utc_timestamp(from_date)
    to_timestamp = convert_date_to_utc_timestamp(to_date) if to_date != "now" else None
    candles = convert_date_range_to_candles(from_date, to_date, timeframe)

    # Validate currency pair format - raises HTTPException 400 if invalid
    currency_pair = validate_currency_pair_or_raise(currency_pair)
    logger.info(f"Range request: {currency_pair} {timeframe} from {from_date} to {to_date} ({candles} candles)")

    # STEP 1: Try getting data from aggregated database first
    aggregated_data, validation_errors = get_validated_range_data(
            currency_pair, timeframe, from_timestamp, to_timestamp, candles, api_call_timestamp
    )

    if aggregated_data:
        logger.info("Found valid data in aggregated database (cache hit)")
        return {
                "sources_used"       : ["aggregated_cache"],
                "sources_failed"     : [],
                "cache_hit"          : True,
                "refreshed_from_apis": False,
                "metadata"           : {
                        "validation_passed": True,
                        "timestamp"        : api_call_timestamp,
                        "date_range"       : f"{from_date} to {to_date}"
                },
                "data"               : aggregated_data
        }

    logger.info(f"Aggregated database validation failed: {validation_errors}")
    logger.info("Attempting to refresh aggregated database from source databases")

    # STEP 2: Try to aggregate from source databases
    aggregation_result = refresh_aggregated_db_by_range(currency_pair, timeframe, from_date, to_date, candles)

    if aggregation_result.success and aggregation_result.data is not None:
        # Validate the aggregated data
        final_data, final_errors = validate_aggregated_range_data(
                aggregation_result.data, timeframe, from_timestamp, to_timestamp, candles, api_call_timestamp
        )

        if final_data:
            logger.info("Successfully aggregated data from source databases")
            response = aggregation_result.to_dict()
            # Remove data from response to add it at the end
            if "data" in response:
                del response["data"]
            response.update({
                    "cache_hit"          : False,
                    "refreshed_from_apis": False,
                    "data"               : final_data
            })
            return response
        else:
            logger.warning(f"Aggregated data validation failed: {final_errors}")

    logger.info("Refreshing source databases from external APIs")

    # STEP 3: Refresh source databases from APIs
    refresh_source_db_by_range(currency_pair, timeframe, from_date, to_date, candles, ecc)

    logger.info("Re-attempting aggregation with fresh source data")

    # STEP 4: Try aggregation again with fresh source data
    final_aggregation_result = refresh_aggregated_db_by_range(currency_pair, timeframe, from_date, to_date, candles)

    if final_aggregation_result.success and final_aggregation_result.data is not None:
        # Validate the final aggregated data
        final_data, final_errors = validate_aggregated_range_data(
                final_aggregation_result.data, timeframe, from_timestamp, to_timestamp, candles, api_call_timestamp
        )

        if final_data:
            logger.info("Successfully aggregated data after API refresh")
            response = final_aggregation_result.to_dict()
            # Remove data from response to add it at the end
            if "data" in response:
                del response["data"]
            response.update({
                    "cache_hit"          : False,
                    "refreshed_from_apis": True,
                    "data"               : final_data
            })
            return response

    # All steps failed
    logger.error("All aggregation attempts failed")

    # Combine all error information
    all_errors = {
            "cache_validation_errors"       : validation_errors,
            "source_aggregation_errors"     : aggregation_result.error_details if aggregation_result else {},
            "api_refresh_aggregation_errors": final_aggregation_result.error_details if final_aggregation_result else {}
    }

    return {
            "error"              : "Failed to retrieve or aggregate OHLC range data from any source",
            "success"            : False,
            "sources_failed"     : final_aggregation_result.sources_failed if final_aggregation_result else [],
            "error_details"      : all_errors,
            "cache_hit"          : False,
            "refreshed_from_apis": True,
            "metadata"           : {
                    "timestamp" : api_call_timestamp,
                    "requested" : f"{currency_pair} {timeframe} from {from_date} to {to_date}",
                    "date_range": f"{from_date} to {to_date}"
            }
    }


def get_validated_range_data(currency_pair: str, timeframe: str, from_timestamp: int,
                             to_timestamp: int, candles: int, api_call_timestamp: int) -> Tuple[
    Optional[List[dict]], List[str]]:
    """Get and validate range data from aggregated database for cache hit."""
    try:
        aggregated_data = get_from_db_by_timestamp(currency_pair, timeframe, from_timestamp, to_timestamp, "aggregated")
    except (ValueError, Exception) as e:
        return None, [str(e)]

    is_valid, errors = _validate_range_data_for_cache(aggregated_data, timeframe, from_timestamp, to_timestamp, candles,
                                                      api_call_timestamp)
    if is_valid:
        return aggregated_data.sort_values(by = 'timestamp', ascending = False).to_dict(orient = 'records'), []
    return None, errors


def validate_aggregated_range_data(df: pd.DataFrame, timeframe: str, from_timestamp: int,
                                   to_timestamp: int, candles: int, api_call_timestamp: int) -> Tuple[
    Optional[List[dict]], List[str]]:
    """
    Final validation of aggregated DataFrame for range data after refresh attempts.

    Args:
        df: Aggregated DataFrame to validate
        timeframe: Expected timeframe
        from_timestamp: Start timestamp
        to_timestamp: End timestamp (can be None)
        candles: Expected number of candles
        api_call_timestamp: Current API call timestamp

    Returns:
        Tuple of (formatted_data, errors) - one will be None
    """
    is_valid, errors = _validate_range_data_final(df, timeframe, from_timestamp, to_timestamp, candles,
                                                  api_call_timestamp)

    if is_valid:
        return df.sort_values(by = 'timestamp', ascending = False).to_dict(orient = 'records'), []

    return None, errors


@log
def _validate_range_data_for_cache(df: Optional[pd.DataFrame], timeframe: str, from_timestamp: int,
                                   to_timestamp: int, candles: int, api_call_timestamp: int) -> Tuple[bool, List[str]]:
    """
    Validate DataFrame for range cache hit - only blocks on structural issues.
    Data quality issues trigger refresh instead of blocking.
    """
    # Structural issues that should block immediately
    if df is None:
        return False, ["DataFrame is None"]
    if not isinstance(df, pd.DataFrame):
        return False, ["Input is not a pandas DataFrame"]
    if df.empty:
        return False, ["DataFrame is empty"]

    # Data quality issues that should trigger refresh (not block)
    refresh_triggers = []
    if not is_data_complete(df, candles):
        refresh_triggers.append("Wrong number of candles")

    # Check for missing timestamps in the specified range
    missing_timestamps = get_missing_timestamps(df, timeframe, from_timestamp, to_timestamp)
    if missing_timestamps:
        refresh_triggers.append(f"Missing timestamps: {missing_timestamps}")

    if refresh_triggers:
        logger.info(f"Range cache validation found refresh triggers: {refresh_triggers}")
        return False, refresh_triggers

    return True, []


@log
def _validate_range_data_final(df: Optional[pd.DataFrame], timeframe: str, from_timestamp: int,
                               to_timestamp: int, candles: int, api_call_timestamp: int) -> Tuple[bool, List[str]]:
    """
    Final validation for range data after refresh attempts - all issues are blocking.
    """
    errors = []

    if df is None:
        return False, ["DataFrame is None"]
    if not isinstance(df, pd.DataFrame):
        return False, ["Input is not a pandas DataFrame"]
    if df.empty:
        errors.append("DataFrame is empty")
    if not is_data_complete(df, candles):
        errors.append("Wrong number of candles")

    # Check for missing timestamps in the specified range
    missing_timestamps = get_missing_timestamps(df, timeframe, from_timestamp, to_timestamp)
    if missing_timestamps:
        errors.append(f"Missing timestamps: {missing_timestamps}")

    # For range data, we don't check freshness as strictly since it's historical data
    if errors:
        logger.warn(f"Range data final validation failed: {errors}")
        return False, errors

    return True, []
