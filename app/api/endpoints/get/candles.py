import pandas as pd
import pytz
from datetime import datetime
from fastapi import APIRouter
from typing import List, Tuple, Optional, Dict, Any

from app.api.endpoints.get.helpers.verify_data_freshness import verify_data_freshness
from app.core.refresh_source_db_by_candles import refresh_source_db_by_candles
from app.core.refresh_aggregated_db_by_candles import refresh_aggregated_db_by_candles
from app.api.endpoints.get.helpers.get_missing_timestamps import get_missing_timestamps
from app.api.endpoints.get.helpers.is_data_complete import is_data_complete
from app.db.sqlite.get_from_db import get_from_db
from app.logger.get_logger import log, logger
from app.core.helpers.validate_currency_pair import validate_currency_pair_or_raise

router = APIRouter()


# TODO: when ran endpoint with /btc-usd/m1/999 it returned this json with errors, analyze the whole flow and explain why this happened and suggested ways to fix it, also when returning the response to a user, missing timestamps should be summarized as a number of gaps:
"""
{
  "error": "Failed to retrieve or aggregate OHLC data from any source",
  "success": false,
  "sources_failed": [],
  "error_details": {
    "cache_validation_errors": [
      "Wrong number of candles",
      "Data is not fresh",
      "Missing timestamps: [np.int64(1749477900), np.int64(1749477960), np.int64(1749478020), np.int64(1749478080), ...[removed for brevity]..., np.int64(1749650580), np.int64(1749650940)]"
    ],
    "source_aggregation_errors": {

    },
    "api_refresh_aggregation_errors": {

    }
  },
  "cache_hit": false,
  "refreshed_from_apis": true,
  "metadata": {
    "timestamp": 1749651554,
    "requested": "btc-usd m1 999"
  }
}"""
@router.get("/{currency_pair}/{timeframe}/{candles}")
@log
def get_candles_endpoint(currency_pair: str, timeframe: str, candles: str) -> Dict[str, Any]:
    """
    Get OHLC candles for a given currency pair and timeframe.

    Flow:
    1. Check aggregated database first (cache layer)
    2. If missing/stale, try to aggregate from source databases
    3. If source databases missing data, refresh from APIs
    4. Re-aggregate and return with source metadata
    """
    api_call_timestamp = int(datetime.now(pytz.utc).timestamp())
    candles_int = int(candles)

    # Validate currency pair format - raises HTTPException 400 if invalid
    currency_pair = validate_currency_pair_or_raise(currency_pair)
    logger.info(f"Checking aggregated database for {currency_pair} {timeframe} {candles} candles")

    # Try getting data from aggregated database first
    aggregated_data, validation_errors = get_validated_data(currency_pair, timeframe, candles, api_call_timestamp)

    if aggregated_data:
        logger.info("Found valid data in aggregated database (cache hit)")
        return {
                "sources_used"       : ["aggregated_cache"],
                "sources_failed"     : [],
                "cache_hit"          : True,
                "refreshed_from_apis": False,
                "metadata"           : {
                        "validation_passed": True,
                        "timestamp"        : api_call_timestamp
                },
                "data"               : aggregated_data
        }

    logger.info(f"Aggregated database validation failed: {validation_errors}")
    logger.info("Attempting to refresh aggregated database from source databases")

    # Try to aggregate from source databases
    aggregation_result = refresh_aggregated_db_by_candles(currency_pair, timeframe, candles_int)

    if aggregation_result.success and aggregation_result.data is not None:
        # Validate the aggregated data
        final_data, final_errors = validate_aggregated_data(aggregation_result.data, timeframe, candles,
                                                            api_call_timestamp)

        if final_data:
            logger.info("Successfully aggregated data from source databases")
            response = aggregation_result.to_dict()
            # Remove data from response to add it at the end
            if "data" in response:
                del response["data"]
            response.update({
                    "cache_hit"          : False,
                    "refreshed_from_apis": False,
                    "data"               : final_data
            })
            return response
        else:
            logger.warning(f"Aggregated data validation failed: {final_errors}")

    logger.info("Refreshing source databases from external APIs")

    # Refresh source databases from APIs
    refresh_source_db_by_candles(currency_pair, timeframe, candles_int)

    logger.info("Re-attempting aggregation with fresh source data")

    # Try aggregation again with fresh source data
    final_aggregation_result = refresh_aggregated_db_by_candles(currency_pair, timeframe, candles_int)

    if final_aggregation_result.success and final_aggregation_result.data is not None:
        # Validate the final aggregated data
        final_data, final_errors = validate_aggregated_data(final_aggregation_result.data, timeframe, candles,
                                                            api_call_timestamp)

        if final_data:
            logger.info("Successfully aggregated data after API refresh")
            response = final_aggregation_result.to_dict()
            # Remove data from response to add it at the end
            if "data" in response:
                del response["data"]
            response.update({
                    "cache_hit"          : False,
                    "refreshed_from_apis": True,
                    "data"               : final_data
            })
            return response

    # All steps failed
    logger.error("All aggregation attempts failed")

    # Combine all error information
    all_errors = {
            "cache_validation_errors"        : validation_errors,
            "source_aggregation_errors"      : aggregation_result.error_details if aggregation_result else {},
            "api_refresh_aggregation_errors" : final_aggregation_result.error_details if final_aggregation_result else {}
    }

    return {
            "error"              : "Failed to retrieve or aggregate OHLC data from any source",
            "success"            : False,
            "sources_failed"     : final_aggregation_result.sources_failed if final_aggregation_result else [],
            "error_details"      : all_errors,
            "cache_hit"          : False,
            "refreshed_from_apis": True,
            "metadata"           : {
                    "timestamp": api_call_timestamp,
                    "requested": f"{currency_pair} {timeframe} {candles}"
            }
    }


def get_validated_data(currency_pair: str, timeframe: str, candles: str,
                       api_call_timestamp: int) -> tuple[list[dict], None] | tuple[None, list[str]]:
    """Get and validate data from database for cache hit."""
    db_data = check_db(currency_pair, timeframe, candles)
    is_valid, errors = _validate_data_for_cache(db_data, timeframe, candles, api_call_timestamp)

    if is_valid:
        return db_data.sort_values(by = 'timestamp', ascending = False).to_dict(orient = 'records'), None
    return None, errors


def check_db(currency_pair: str, timeframe: str, candles: str) -> Optional[pd.DataFrame]:
    """Safely retrieve data from aggregated database only."""
    try:
        return get_from_db(currency_pair, timeframe, candles, "aggregated")
    except (ValueError, Exception):
        return None


def _validate_data_for_cache(df: Optional[pd.DataFrame], timeframe: str, candles: str,
                            api_call_timestamp: int) -> Tuple[bool, List[str]]:
    """
    Validate DataFrame for cache hit - only blocks on structural issues.
    Data quality issues (freshness, completeness) trigger refresh instead of blocking.
    """
    # Structural issues that should block immediately
    if df is None:
        return False, ["DataFrame is None"]
    if not isinstance(df, pd.DataFrame):
        return False, ["Input is not a pandas DataFrame"]
    if df.empty:
        return False, ["DataFrame is empty"]

    # Data quality issues that should trigger refresh (not block)
    refresh_triggers = []
    if not is_data_complete(df, candles):
        refresh_triggers.append("Wrong number of candles")
    if not verify_data_freshness(df, timeframe, api_call_timestamp):
        refresh_triggers.append("Data is not fresh")
    missing_timestamps = get_missing_timestamps(df, timeframe, None, None)
    if missing_timestamps:
        refresh_triggers.append(f"Missing timestamps: {missing_timestamps}")

    if refresh_triggers:
        logger.info(f"Cache validation found refresh triggers: {refresh_triggers}")
        return False, refresh_triggers

    return True, []


def _validate_data_final(df: Optional[pd.DataFrame], timeframe: str, candles: str,
                        api_call_timestamp: int) -> Tuple[bool, List[str]]:
    """
    Final validation after refresh attempts - all issues are blocking.
    """
    errors = []

    if df is None:
        return False, ["DataFrame is None"]
    if not isinstance(df, pd.DataFrame):
        return False, ["Input is not a pandas DataFrame"]
    if df.empty:
        errors.append("DataFrame is empty")
    if not is_data_complete(df, candles):
        errors.append("Wrong number of candles")
    if not verify_data_freshness(df, timeframe, api_call_timestamp):
        errors.append("Data is not fresh")
    missing_timestamps = get_missing_timestamps(df, timeframe, None, None)
    if missing_timestamps:
        errors.append(f"Missing timestamps: {missing_timestamps}")
    if errors:
        logger.warn(f"Final validation failed: {errors}")
        return False, errors

    return True, []


def validate_aggregated_data(df: pd.DataFrame, timeframe: str, candles: str,
                             api_call_timestamp: int) -> Tuple[Optional[List[dict]], List[str]]:
    """
    Final validation of aggregated DataFrame after refresh attempts.

    Args:
        df: Aggregated DataFrame to validate
        timeframe: Expected timeframe
        candles: Expected number of candles
        api_call_timestamp: Current API call timestamp

    Returns:
        Tuple of (formatted_data, errors) - one will be None
    """
    is_valid, errors = _validate_data_final(df, timeframe, candles, api_call_timestamp)

    if is_valid:
        return df.sort_values(by = 'timestamp', ascending = False).to_dict(orient = 'records'), []

    return None, errors
