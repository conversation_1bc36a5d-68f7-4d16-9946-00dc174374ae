import re
from fastapi import HTTPException
from app.logger.get_logger import log, logger


@log
def validate_currency_pair_or_raise(currency_pair: str) -> str:
    """
    Validate currency pair format and return normalized version.

    Args:
        currency_pair: String to validate

    Returns:
        str: Normalized currency pair (lowercase)

    Raises:
        HTTPException: 400 if invalid format
    """
    if not currency_pair:
        raise HTTPException(
                status_code = 400,
                detail = "Currency pair cannot be empty"
        )

    # Convert to lowercase for validation
    pair_lower = currency_pair.lower()

    # Pattern: 3-4 lowercase letters, hyphen, 3-4 lowercase letters
    pattern = r'^[a-z]{3,4}-[a-z]{3,4}$'

    if not re.match(pattern, pair_lower):
        raise HTTPException(
                status_code = 400,
                detail = f"Invalid currency pair format: '{currency_pair}'. Expected format: 'base-quote' (e.g., 'btc-usd')"
        )

    logger.info(f"Currency pair format validation passed: {currency_pair}")
    return pair_lower
