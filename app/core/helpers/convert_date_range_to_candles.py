from datetime import datetime

from dateutil import tz
from app.logger.get_logger import log


@log
def convert_date_range_to_candles(from_date: str, to_date: str, timeframe: str) -> int:
    """
    Converts a date range and a timeframe to the number of candles.

    Parameters
    ----------
    from_date : str
        The start date in the format "%d%m%Y".
    to_date : str
        The end date in the format "%d%m%Y".
    timeframe : str
        The timeframe, e.g. "m1", "m5", "m15", "h1", "h4", or "d1".

    Returns
    -------
    int
        The number of candles in the date range for the given timeframe.

    Raises
    ------
    ValueError
        If the given timeframe is invalid.
    """
    utc = tz.tzutc()
    # Parse the start and end dates
    start_date = datetime.strptime(from_date, "%d%m%Y").replace(tzinfo = utc)

    # Check if end is "now" and use current UTC timestamp if so
    if to_date.lower() == "now":
        end_date = datetime.fromtimestamp(datetime.now(utc).timestamp(), utc)
        # print(end_date)
    else:
        end_date = datetime.strptime(to_date, "%d%m%Y").replace(tzinfo = utc)

    # Calculate the time difference
    # print(end_date, start_date)
    time_diff = end_date - start_date

    # Map timeframe to seconds
    timeframe_to_seconds = {
            "m1" : 60,
            "m5" : 300,
            "m15": 900,
            "h1" : 3600,
            "h4" : 14400,
            "d1" : 86400
    }

    # Determine the number of candles based on the timeframe
    if timeframe in timeframe_to_seconds:
        candles = time_diff.total_seconds() // timeframe_to_seconds[timeframe]
        # Add 1 to include the final timestamp (e.g., 00:00:00 of the end date)
        # For range 01032025 to 02032025, this includes both 00:00:00 of day 1 and 00:00:00 of day 2
        candles += 1
    else:
        raise ValueError("Invalid timeframe")

    return int(candles)
